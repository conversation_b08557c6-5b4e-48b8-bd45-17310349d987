package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;

import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.products.microsoft.sharepoint.SharepointConstants;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.ShellCommandResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SPAzureADB2BIntegrationRemediator
 * Tests CIS MS365 benchmark 7.2.2: Ensure SharePoint and OneDrive integration with Azure AD B2B is enabled
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SharePoint Azure AD B2B Integration Remediator Tests")
class SPAzureADB2BIntegrationRemediatorTest {

    @Mock
    private PowerShellSharepointClient mockClient;

    @Mock
    private SharePointTenantProperties mockTenant;

    @Mock
    private GeneralResult mockGeneralResult;

    @Captor
    private ArgumentCaptor<SPShellCommand<GeneralResult>> commandCaptor;

    private SPAzureADB2BIntegrationRemediator remediator;

    @BeforeEach
    void setUp() {
        remediator = new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant, null);
    }

    @Nested
    @DisplayName("Remediation Tests")
    class RemediationTests {

        @Test
        @DisplayName("Should succeed when Azure AD B2B integration is already enabled")
        void shouldSucceedWhenAlreadyEnabled() {
            // Given
            mockTenant.enableAzureADB2BIntegration = true;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo(SharepointConstants.AZURE_ADB2B_INTEGRATION_SUCCESS);
        }

        @Test
        @DisplayName("Should successfully enable Azure AD B2B integration when disabled")
        void shouldSuccessfullyEnableAzureADB2BIntegrationWhenDisabled() {
            // Given
            mockTenant.enableAzureADB2BIntegration = false;
            mockTenant.objectIdentity = "test-tenant-id";

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            // Set errorInfo to null to indicate success (no error)
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(commandCaptor.capture());

            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertThat(capturedCommand).isNotNull();
        }

        @Test
        @DisplayName("Should fail when tenant properties are not available")
        void shouldFailWhenTenantPropertiesNotAvailable() {
            // Given
            remediator = new SPAzureADB2BIntegrationRemediator(mockClient, null, null);

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("SharePoint tenant properties are not available");
        }

        @Test
        @DisplayName("Should successfully enable Azure AD B2B integration when null")
        void shouldSuccessfullyEnableAzureADB2BIntegrationWhenNull() {
            // Given
            mockTenant.enableAzureADB2BIntegration = null;
            mockTenant.objectIdentity = "test-tenant-id";

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            // Set errorInfo to null to indicate success (no error)
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(commandCaptor.capture());

            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertThat(capturedCommand).isNotNull();
        }

        @Test
        @DisplayName("Should handle execution exception gracefully")
        void shouldHandleExecutionExceptionGracefully() {
            // Given
            mockTenant.enableAzureADB2BIntegration = false;
            mockTenant.objectIdentity = "test-tenant-id";

            RuntimeException testException = new RuntimeException("Test exception");
            when(mockClient.execute_(any())).thenReturn(CompletableFuture.failedFuture(testException));

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).contains("Failed to enable Azure AD B2B integration");
        }

        @Test
        @DisplayName("Should use correct PowerShell property and command structure")
        void shouldUseCorrectPowerShellPropertyAndCommand() {
            // Given
            mockTenant.enableAzureADB2BIntegration = false;
            mockTenant.objectIdentity = "test-tenant-id";

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            verify(mockClient).execute_(commandCaptor.capture());
            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            
            // Verify command structure (implementation specific)
            assertThat(capturedCommand).isNotNull();
            // The actual command validation would depend on SPShellCommand implementation
        }
    }

    @Nested
    @DisplayName("Rollback Tests")
    class RollbackTests {

        @Test
        @DisplayName("Should fail rollback when fix result is null")
        void shouldFailRollbackWhenFixResultIsNull() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant);

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(null);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("No fix result provided for rollback");
        }

        @Test
        @DisplayName("Should fail rollback when no changes to rollback")
        void shouldFailRollbackWhenNoChanges() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant);
            
            PolicyChangeResult fixResult = new PolicyChangeResult()
                .changes(Collections.emptyList());

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("No changes to rollback");
        }

        @Test
        @DisplayName("Should fail rollback when tenant properties not available")
        void shouldFailRollbackWhenTenantNotAvailable() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, null);
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(false)
                .newValue(true);
            fixResult.changes(Collections.singletonList(paramChange));

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("SharePoint tenant properties not available for rollback");
        }

        @Test
        @DisplayName("Should fail rollback when previous value is null")
        void shouldFailRollbackWhenPreviousValueIsNull() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant);
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(null)  // Previous value is null
                .newValue(true);
            fixResult.changes(Collections.singletonList(paramChange));

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("Previous value is null, cannot perform rollback");
        }

        @Test
        @DisplayName("Should fail rollback when new value is null")
        void shouldFailRollbackWhenNewValueIsNull() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant);
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(false)
                .newValue(null);  // New value is null
            fixResult.changes(Collections.singletonList(paramChange));

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("New value is null, cannot perform rollback");
        }

        @Test
        @DisplayName("Should successfully rollback changes")
        void shouldSuccessfullyRollbackChanges() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant);
            
            mockTenant.objectIdentity = "test-tenant-id";

            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(false)  // Previous value was false
                .newValue(true);   // New value was true
            fixResult.changes(Collections.singletonList(paramChange));

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            // Set errorInfo to null to indicate success (no error)
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(commandCaptor.capture());

            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertThat(capturedCommand).isNotNull();
        }

        @Test
        @DisplayName("Should handle rollback execution exception gracefully")
        void shouldHandleRollbackExecutionExceptionGracefully() {
            // Given
            SPAzureADB2BIntegrationRemediator rollbackRemediator = 
                new SPAzureADB2BIntegrationRemediator(mockClient, mockTenant);
            
            mockTenant.objectIdentity = "test-tenant-id";

            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(false)
                .newValue(true);
            fixResult.changes(Collections.singletonList(paramChange));

            RuntimeException testException = new RuntimeException("Rollback test exception");
            when(mockClient.execute_(any())).thenReturn(CompletableFuture.failedFuture(testException));

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.7.1v1");
            assertThat(changeResult.getDesc()).contains("Rollback failed");
        }
    }

    @Nested
    @DisplayName("JSON Remediation Tests")
    class JsonRemediationTests {

        @Test
        @DisplayName("Should return JSON representation of remediation result")
        void shouldReturnJsonRepresentation() {
            // Given
            mockTenant.enableAzureADB2BIntegration = true;

            // When
            CompletableFuture<JsonNode> result = remediator.remediate();

            // Then
            JsonNode jsonResult = result.join();
            assertThat(jsonResult).isNotNull();
            assertThat(jsonResult.get("Result").asText()).isEqualTo("SUCCESS");
            assertThat(jsonResult.get("PolicyId").asText()).isEqualTo("MS.SHAREPOINT.7.1v1");
        }

        @Test
        @DisplayName("Should return JSON representation when remediation fails")
        void shouldReturnJsonRepresentationWhenRemediationFails() {
            // Given
            remediator = new SPAzureADB2BIntegrationRemediator(mockClient, null, null);

            // When
            CompletableFuture<JsonNode> result = remediator.remediate();

            // Then
            JsonNode jsonResult = result.join();
            assertThat(jsonResult).isNotNull();
            assertThat(jsonResult.get("Result").asText()).isEqualTo("FAILED");
            assertThat(jsonResult.get("PolicyId").asText()).isEqualTo("MS.SHAREPOINT.7.1v1");
        }
    }

    @Nested
    @DisplayName("Policy ID Tests")
    class PolicyIdTests {

        @Test
        @DisplayName("Should return correct policy ID")
        void shouldReturnCorrectPolicyId() {
            // When
            String policyId = remediator.getPolicyId();

            // Then
            assertThat(policyId).isEqualTo("MS.SHAREPOINT.7.1v1");
        }
    }

    @Nested
    @DisplayName("CIS Compliance Tests")
    class CISComplianceTests {

        @Test
        @DisplayName("Should implement CIS MS365 benchmark 7.2.2 correctly")
        void shouldImplementCISBenchmark722Correctly() {
            // Given - Test the specific CIS requirement
            mockTenant.enableAzureADB2BIntegration = false;  // Default should be false per CIS
            mockTenant.objectIdentity = "test-tenant-id";

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            mockGeneralResult.errorInfo = null;

            // When - Remediate to enable the setting (CIS requirement)
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then - Should attempt to set EnableAzureADB2BIntegration to true
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(any());
        }

        @Test
        @DisplayName("Should use correct PowerShell property name for CIS 7.2.2")
        void shouldUseCorrectPowerShellPropertyNameForCIS722() {
            // Verify that we're using the correct PowerShell property name
            // as specified in CIS benchmark 7.2.2
            String expectedProperty = SharepointConstants.ENABLE_AZURE_ADB2B_INTEGRATION_PROPERTY;
            assertThat(expectedProperty).isEqualTo("EnableAzureADB2BIntegration");
        }
    }
}